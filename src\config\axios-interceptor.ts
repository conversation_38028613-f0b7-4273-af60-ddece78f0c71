/* eslint-disable @typescript-eslint/no-explicit-any */
import { ACCESS_TOKEN, REFRESH_TOKEN, TIME_OUT_REQUEST } from '@/src/constants'
import axios, { type AxiosResponse } from 'axios'
import cookie from 'cookiejs'
// import axiosRetry from 'axios-retry'

const TIMEOUT = TIME_OUT_REQUEST * 60 * 1000
// const NUMBER_OF_RETRIES = 0

axios.defaults.timeout = TIMEOUT
axios.defaults.baseURL =
  (window as any).RUNTIME_CONFIG?.VITE_SERVER_API_URL ||
  import.meta.env.VITE_SERVER_API_URL

const instance = axios.create({
  baseURL:
    (window as any).RUNTIME_CONFIG?.VITE_SERVER_API_URL ||
    import.meta.env.VITE_SERVER_API_URL,
  timeout: TIMEOUT,
  // withCredentials: true,
})

// axiosRetry(instance, {
//   retries: NUMBER_OF_RETRIES,
//   retryCondition: error => error.response?.status === 401,
// })

let isRefreshing = false
let queuedRequests: ((token: string) => void)[] = []

function queueRequest(cb: (token: string) => void) {
  queuedRequests.push(cb)
}

function resolveQueuedRequests(newToken: string) {
  queuedRequests.forEach(cb => cb(newToken))
  queuedRequests = []
}

const onRequestSuccess = (config: any) => {
  const accessToken = cookie.get(ACCESS_TOKEN)
  const LANGUAGE = localStorage.getItem('app_locale') ?? 'en'
  config.headers['Accept-Language'] = LANGUAGE === 'vi' ? 'vi-VN' : LANGUAGE
  // config.headers['ngrok-skip-browser-warning'] = 'true'
  config.headers['Accept-Language'] = LANGUAGE
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`
  }
  return config
}

const onResponseSuccess = (response: AxiosResponse) => response

const onResponseError = async (error: any) => {
  const originalRequest = error.config
  if (
    error.response?.status === 401 &&
    !originalRequest._retry &&
    !originalRequest.url.includes('/connect/token')
  ) {
    originalRequest._retry = true

    if (isRefreshing) {
      return new Promise(resolve => {
        queueRequest(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          resolve(instance(originalRequest))
        })
      })
    }

    isRefreshing = true

    try {
      const refreshToken = cookie.get(REFRESH_TOKEN)
      if (!refreshToken) {
        localStorage.removeItem('airportGlobal')
        throw new Error('No refresh token')
      }

      const response = await axios.post(
        '/connect/token',
        {
          grant_type: 'refresh_token',
          client_id:
            (window as any).RUNTIME_CONFIG?.VITE_CLIENT_ID ||
            import.meta.env.VITE_CLIENT_ID,
          scope:
            (window as any).RUNTIME_CONFIG?.VITE_SCOPE ||
            import.meta.env.VITE_SCOPE,
          refresh_token: refreshToken,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      )

      const { access_token: accessToken, refresh_token: newRefreshToken } =
        response.data

      cookie.set(ACCESS_TOKEN, accessToken)
      cookie.set(REFRESH_TOKEN, newRefreshToken)

      instance.defaults.headers.common.Authorization = `Bearer ${accessToken}`
      resolveQueuedRequests(accessToken)

      originalRequest.headers.Authorization = `Bearer ${accessToken}`
      return instance(originalRequest)
    } catch (err) {
      cookie.remove(ACCESS_TOKEN)
      cookie.remove(REFRESH_TOKEN)
      window.location.href = '/login-ldap'
      return Promise.reject(err)
    } finally {
      isRefreshing = false
    }
  }
  return Promise.reject(error)
}

instance.interceptors.request.use(onRequestSuccess)
instance.interceptors.response.use(onResponseSuccess, onResponseError)

export default instance
